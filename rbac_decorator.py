"""
Role-Based Access Control (RBAC) Decorator
Simple implementation for Computer Shop Management System
"""

from functools import wraps
from flask import session, flash, redirect, url_for, abort, request, jsonify

def require_role(*allowed_roles):
    """
    Decorator to restrict access to routes based on user roles.
    
    Usage:
        @require_role('SUPER_ADMIN')
        @require_role('SUPER_ADMIN', 'ADMIN')
        @require_role('SUPER_ADMIN', 'ADMIN', 'STAFF')
    
    Args:
        *allowed_roles: Variable number of role strings that are allowed to access the route
    
    Returns:
        Decorator function that checks user role before allowing access
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Check if user is logged in
            if 'username' not in session:
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('auth.login'))
            
            # Get user role from session (convert to uppercase for consistency)
            user_role = session.get('role', '').upper()
            
            # Convert allowed roles to uppercase for comparison
            allowed_roles_upper = [role.upper() for role in allowed_roles]
            
            # Check if user has required role
            if user_role not in allowed_roles_upper:
                # Handle AJAX requests differently
                if request.is_json or request.headers.get('Content-Type') == 'application/json':
                    return jsonify({
                        'success': False,
                        'error': 'Access denied. Insufficient privileges.',
                        'required_roles': list(allowed_roles),
                        'user_role': user_role
                    }), 403
                
                # For regular requests, show error and redirect
                flash(f'Access denied. This page requires {" or ".join(allowed_roles)} privileges.', 'error')
                
                # Redirect to appropriate page based on user role
                if user_role == 'STAFF':
                    return redirect(url_for('auth.walk_in_sales'))
                elif user_role == 'ADMIN':
                    return redirect(url_for('auth.staff_dashboard'))
                else:
                    return redirect(url_for('auth.login'))
            
            # User has required role, proceed with the original function
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def check_role(required_roles):
    """
    Simple function to check if current user has required role.
    
    Args:
        required_roles: String or list of role strings
    
    Returns:
        Boolean indicating if user has required role
    """
    if isinstance(required_roles, str):
        required_roles = [required_roles]
    
    # Check if user is logged in
    if 'username' not in session:
        return False
    
    # Get user role from session
    user_role = session.get('role', '').upper()
    
    # Convert required roles to uppercase
    required_roles_upper = [role.upper() for role in required_roles]
    
    return user_role in required_roles_upper

def get_user_role():
    """
    Get the current user's role from session.
    
    Returns:
        String representing user role, or 'GUEST' if not logged in
    """
    if 'username' not in session:
        return 'GUEST'
    
    return session.get('role', 'STAFF').upper()

def is_admin():
    """Check if current user is Admin or Super Admin"""
    return check_role(['ADMIN', 'SUPER_ADMIN'])

def is_super_admin():
    """Check if current user is Super Admin"""
    return check_role(['SUPER_ADMIN'])

def is_staff():
    """Check if current user is Staff (any role)"""
    return check_role(['STAFF', 'ADMIN', 'SUPER_ADMIN'])

# Template helper functions
def register_rbac_helpers(app):
    """
    Register RBAC helper functions for use in templates.
    Call this function in your app initialization.
    
    Usage in templates:
        {% if check_role(['ADMIN', 'SUPER_ADMIN']) %}
            <button>Admin Only Button</button>
        {% endif %}
    """
    
    @app.context_processor
    def inject_rbac_helpers():
        return {
            'check_role': check_role,
            'get_user_role': get_user_role,
            'is_admin': is_admin,
            'is_super_admin': is_super_admin,
            'is_staff': is_staff
        }

# Example usage in your routes:
"""
from rbac_decorator import require_role

# Super Admin only
@app.route('/user-management')
@require_role('SUPER_ADMIN')
def user_management():
    return render_template('user_management.html')

# Admin and Super Admin
@app.route('/staff/inventory')
@require_role('SUPER_ADMIN', 'ADMIN')
def staff_inventory():
    return render_template('staff_inventory.html')

# All staff roles
@app.route('/walk-in-sales')
@require_role('SUPER_ADMIN', 'ADMIN', 'STAFF')
def walk_in_sales():
    return render_template('walk_in_sales.html')
"""
