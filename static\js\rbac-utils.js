/**
 * Role-Based Access Control (RBAC) Utilities
 * Simple implementation for Computer Shop Management System
 * Supports roles: SUPER_ADMIN, ADMIN, STAFF
 */

class RBACManager {
    constructor() {
        this.userRole = this.getUserRole();
        this.initializeRoleBasedUI();
        console.log(`[RBAC] Initialized for role: ${this.userRole}`);
    }

    getUserRole() {
        // Get user role from data attribute, hidden element, or default to STAFF
        return document.body.dataset.userRole || 
               document.getElementById('user-role')?.value || 
               document.querySelector('[data-user-role]')?.dataset.userRole ||
               'STAFF'; // Default to most restrictive role
    }

    hasPermission(requiredRoles) {
        if (typeof requiredRoles === 'string') {
            requiredRoles = [requiredRoles];
        }
        return requiredRoles.includes(this.userRole);
    }

    initializeRoleBasedUI() {
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.hideRestrictedElements();
                this.setupRoleBasedEventListeners();
            });
        } else {
            this.hideRestrictedElements();
            this.setupRoleBasedEventListeners();
        }
    }

    hideRestrictedElements() {
        // Define what each role cannot see
        const restrictedSelectors = {
            'SUPER_ADMIN': [], // Super admin can see everything
            'ADMIN': ['.super-admin-only'],
            'STAFF': ['.super-admin-only', '.admin-only', '.manager-only']
        };

        const elementsToHide = restrictedSelectors[this.userRole] || restrictedSelectors['STAFF'];
        
        // Hide restricted elements
        elementsToHide.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                element.style.display = 'none';
                element.setAttribute('data-rbac-hidden', 'true');
            });
        });

        // Hide sensitive data for staff
        if (this.userRole === 'STAFF') {
            this.hideSensitiveData();
        }

        console.log(`[RBAC] Hidden ${elementsToHide.length} element types for role: ${this.userRole}`);
    }

    hideSensitiveData() {
        // Hide financial and sensitive information from staff
        const sensitiveSelectors = [
            '.cost-info',
            '.profit-info', 
            '.original-price',
            '.profit-margin',
            '.cost-price',
            '[data-sensitive="cost"]',
            '[data-sensitive="profit"]'
        ];

        sensitiveSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                element.style.display = 'none';
                element.setAttribute('data-rbac-hidden', 'true');
            });
        });

        // Replace sensitive text content with asterisks
        document.querySelectorAll('.sensitive-text').forEach(element => {
            if (!element.dataset.originalText) {
                element.dataset.originalText = element.textContent;
            }
            element.textContent = '***';
        });

        console.log('[RBAC] Hidden sensitive data for STAFF role');
    }

    setupRoleBasedEventListeners() {
        // Prevent unauthorized actions by intercepting clicks
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-required-role]');
            if (target) {
                const requiredRoles = target.dataset.requiredRole.split(',').map(role => role.trim());
                if (!this.hasPermission(requiredRoles)) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showAccessDeniedMessage(target);
                    return false;
                }
            }
        });

        // Also prevent form submissions for restricted forms
        document.addEventListener('submit', (e) => {
            const form = e.target.closest('[data-required-role]');
            if (form) {
                const requiredRoles = form.dataset.requiredRole.split(',').map(role => role.trim());
                if (!this.hasPermission(requiredRoles)) {
                    e.preventDefault();
                    this.showAccessDeniedMessage(form);
                    return false;
                }
            }
        });
    }

    showAccessDeniedMessage(element = null) {
        const actionName = element?.dataset.actionName || 'perform this action';
        const message = `Access denied. You need ${element?.dataset.requiredRole || 'higher privileges'} to ${actionName}.`;

        // Use SweetAlert if available, otherwise use regular alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Access Denied',
                text: message,
                icon: 'warning',
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc3545'
            });
        } else if (typeof alert !== 'undefined') {
            alert(message);
        } else {
            console.warn('[RBAC] Access denied:', message);
        }
    }

    // Utility methods for checking specific permissions
    canManageProducts() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    canManageUsers() {
        return this.hasPermission(['SUPER_ADMIN']);
    }

    canViewReports() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    canProcessSales() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN', 'STAFF']);
    }

    canManageDiscounts() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    canManageCustomers() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    canViewFinancialData() {
        return this.hasPermission(['SUPER_ADMIN']);
    }

    canManageInventory() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    // Method to refresh RBAC after dynamic content changes
    refresh() {
        this.hideRestrictedElements();
        console.log('[RBAC] Refreshed role-based restrictions');
    }

    // Method to temporarily show hidden elements (for debugging)
    showAll() {
        document.querySelectorAll('[data-rbac-hidden]').forEach(element => {
            element.style.display = '';
            element.removeAttribute('data-rbac-hidden');
        });
        console.log('[RBAC] Temporarily showing all hidden elements');
    }

    // Method to get role hierarchy level (for comparison)
    getRoleLevel() {
        const roleLevels = {
            'STAFF': 1,
            'ADMIN': 2,
            'SUPER_ADMIN': 3
        };
        return roleLevels[this.userRole] || 0;
    }

    // Method to check if current user has higher or equal role
    hasRoleLevel(minimumRole) {
        const roleLevels = {
            'STAFF': 1,
            'ADMIN': 2,
            'SUPER_ADMIN': 3
        };
        return this.getRoleLevel() >= (roleLevels[minimumRole] || 0);
    }
}

// Auto-initialize when script loads
let rbacManager;

// Initialize immediately if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        rbacManager = new RBACManager();
        window.rbac = rbacManager;
    });
} else {
    rbacManager = new RBACManager();
    window.rbac = rbacManager;
}

// Make classes globally available
window.RBACManager = RBACManager;

// Export for module systems if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { RBACManager };
}

console.log('[RBAC] RBAC utilities loaded successfully');
