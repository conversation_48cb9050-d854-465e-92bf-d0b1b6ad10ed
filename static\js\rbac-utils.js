/**
 * Role-Based Access Control (RBAC) Utilities
 * Simple implementation for Computer Shop Management System
 * Supports roles: SUPER_ADMIN, ADMIN, STAFF
 */

class RBACManager {
    constructor() {
        this.userRole = this.getUserRole();
        this.initializeRoleBasedUI();
        console.log(`[RBAC] Initialized for role: ${this.userRole}`);
    }

    getUserRole() {
        // Get user role from data attribute, hidden element, or default to STAFF
        return document.body.dataset.userRole || 
               document.getElementById('user-role')?.value || 
               document.querySelector('[data-user-role]')?.dataset.userRole ||
               'STAFF'; // Default to most restrictive role
    }

    hasPermission(requiredRoles) {
        if (typeof requiredRoles === 'string') {
            requiredRoles = [requiredRoles];
        }
        return requiredRoles.includes(this.userRole);
    }

    initializeRoleBasedUI() {
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.hideRestrictedElements();
                this.setupRoleBasedEventListeners();
            });
        } else {
            this.hideRestrictedElements();
            this.setupRoleBasedEventListeners();
        }
    }

    hideRestrictedElements() {
        // Define what each role cannot see
        const restrictedSelectors = {
            'SUPER_ADMIN': [], // Super admin can see everything
            'ADMIN': ['.super-admin-only'],
            'STAFF': ['.super-admin-only', '.admin-only', '.manager-only']
        };

        const elementsToHide = restrictedSelectors[this.userRole] || restrictedSelectors['STAFF'];

        // Hide restricted elements with important CSS to override any inline styles
        elementsToHide.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                element.style.setProperty('display', 'none', 'important');
                element.setAttribute('data-rbac-hidden', 'true');
                element.setAttribute('aria-hidden', 'true');
                // Also disable the element to prevent any interaction
                if (element.tagName === 'BUTTON' || element.tagName === 'INPUT' || element.tagName === 'SELECT') {
                    element.disabled = true;
                }
            });
        });

        // Hide sensitive data for staff
        if (this.userRole === 'STAFF') {
            this.hideSensitiveData();
        }

        console.log(`[RBAC] Hidden ${elementsToHide.length} element types for role: ${this.userRole}`);
    }

    hideSensitiveData() {
        // For STAFF users, hide only highly sensitive financial information
        // but allow them to see product prices for customer service
        const sensitiveSelectors = [
            '.profit-info',
            '.profit-margin',
            '.cost-price',
            '[data-sensitive="cost"]',
            '[data-sensitive="profit"]',
            '.financial-only' // New class for financial data
        ];

        sensitiveSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                element.style.display = 'none';
                element.setAttribute('data-rbac-hidden', 'true');
            });
        });

        // Replace sensitive text content with asterisks (only for profit/cost data)
        document.querySelectorAll('.sensitive-text[data-type="profit"], .sensitive-text[data-type="cost"]').forEach(element => {
            if (!element.dataset.originalText) {
                element.dataset.originalText = element.textContent;
            }
            element.textContent = '***';
        });

        // Add read-only indicators to pages for STAFF
        this.addReadOnlyIndicators();

        console.log('[RBAC] Hidden sensitive financial data for STAFF role (prices visible for customer service)');
    }

    addReadOnlyIndicators() {
        // Add visual indicators that STAFF is in read-only mode
        const pageTitle = document.querySelector('h1');
        if (pageTitle && !pageTitle.querySelector('.read-only-badge')) {
            const readOnlyBadge = document.createElement('span');
            readOnlyBadge.className = 'read-only-badge';
            readOnlyBadge.innerHTML = '<i class="fas fa-eye"></i> View Only';
            readOnlyBadge.style.cssText = `
                background: #6c757d;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.7em;
                margin-left: 10px;
                font-weight: normal;
            `;
            pageTitle.appendChild(readOnlyBadge);
        }

        // Add read-only styling to tables
        document.querySelectorAll('table').forEach(table => {
            if (this.userRole === 'STAFF') {
                table.style.opacity = '0.9';
                table.style.pointerEvents = 'auto'; // Allow viewing but buttons will be disabled
            }
        });
    }

    setupRoleBasedEventListeners() {
        // Prevent unauthorized actions by intercepting clicks
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-required-role]');
            if (target) {
                const requiredRoles = target.dataset.requiredRole.split(',').map(role => role.trim());
                if (!this.hasPermission(requiredRoles)) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showAccessDeniedMessage(target);
                    return false;
                }
            }
        });

        // Also prevent form submissions for restricted forms
        document.addEventListener('submit', (e) => {
            const form = e.target.closest('[data-required-role]');
            if (form) {
                const requiredRoles = form.dataset.requiredRole.split(',').map(role => role.trim());
                if (!this.hasPermission(requiredRoles)) {
                    e.preventDefault();
                    this.showAccessDeniedMessage(form);
                    return false;
                }
            }
        });
    }

    showAccessDeniedMessage(element = null) {
        const actionName = element?.dataset.actionName || 'perform this action';
        const message = `Access denied. You need ${element?.dataset.requiredRole || 'higher privileges'} to ${actionName}.`;

        // Use SweetAlert if available, otherwise use regular alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Access Denied',
                text: message,
                icon: 'warning',
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc3545'
            });
        } else if (typeof alert !== 'undefined') {
            alert(message);
        } else {
            console.warn('[RBAC] Access denied:', message);
        }
    }

    // Utility methods for checking specific permissions
    canManageProducts() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    canManageUsers() {
        return this.hasPermission(['SUPER_ADMIN']);
    }

    canViewReports() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    canProcessSales() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN', 'STAFF']);
    }

    canManageDiscounts() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    canManageCustomers() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    canViewFinancialData() {
        return this.hasPermission(['SUPER_ADMIN']);
    }

    canManageInventory() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    // New methods for read-only access checking
    canViewProducts() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN', 'STAFF']);
    }

    canViewCustomers() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN', 'STAFF']);
    }

    canViewOrders() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN', 'STAFF']);
    }

    canViewPreorders() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN', 'STAFF']);
    }

    // Check if user has read-only access (STAFF) vs full access (ADMIN/SUPER_ADMIN)
    isReadOnlyAccess() {
        return this.userRole === 'STAFF';
    }

    hasFullAccess() {
        return this.hasPermission(['SUPER_ADMIN', 'ADMIN']);
    }

    // Method to refresh RBAC after dynamic content changes
    refresh() {
        this.hideRestrictedElements();
        console.log('[RBAC] Refreshed role-based restrictions');
    }

    // Method to temporarily show hidden elements (for debugging)
    showAll() {
        document.querySelectorAll('[data-rbac-hidden]').forEach(element => {
            element.style.display = '';
            element.removeAttribute('data-rbac-hidden');
        });
        console.log('[RBAC] Temporarily showing all hidden elements');
    }

    // Method to get role hierarchy level (for comparison)
    getRoleLevel() {
        const roleLevels = {
            'STAFF': 1,
            'ADMIN': 2,
            'SUPER_ADMIN': 3
        };
        return roleLevels[this.userRole] || 0;
    }

    // Method to check if current user has higher or equal role
    hasRoleLevel(minimumRole) {
        const roleLevels = {
            'STAFF': 1,
            'ADMIN': 2,
            'SUPER_ADMIN': 3
        };
        return this.getRoleLevel() >= (roleLevels[minimumRole] || 0);
    }

    // Debug method to show RBAC status
    debugStatus() {
        console.log('=== RBAC Debug Status ===');
        console.log('Current Role:', this.userRole);
        console.log('Can Manage Products:', this.canManageProducts());
        console.log('Can Manage Users:', this.canManageUsers());
        console.log('Can View Reports:', this.canViewReports());
        console.log('Can Process Sales:', this.canProcessSales());
        console.log('Is Read Only:', this.isReadOnlyAccess());
        console.log('Has Full Access:', this.hasFullAccess());

        const hiddenElements = document.querySelectorAll('[data-rbac-hidden="true"]');
        console.log('Hidden Elements Count:', hiddenElements.length);

        const adminOnlyElements = document.querySelectorAll('.admin-only');
        console.log('Admin-only Elements Found:', adminOnlyElements.length);

        console.log('========================');
    }
}

// Auto-initialize when script loads
let rbacManager;

// Initialize immediately if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        rbacManager = new RBACManager();
        window.rbac = rbacManager;
        // Add global debug function
        window.debugRBAC = () => rbacManager.debugStatus();
    });
} else {
    rbacManager = new RBACManager();
    window.rbac = rbacManager;
    // Add global debug function
    window.debugRBAC = () => rbacManager.debugStatus();
}

// Make classes globally available
window.RBACManager = RBACManager;

// Export for module systems if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { RBACManager };
}

console.log('[RBAC] RBAC utilities loaded successfully');
