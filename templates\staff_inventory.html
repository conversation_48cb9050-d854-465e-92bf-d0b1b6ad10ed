<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products Management</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <style>
        .staff-container {
            width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .inventory-actions {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
            gap: 15px;
        }

        .inventory-search {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            max-width: 1000px;
           
        }

        .inventory-search input {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 5px;
            flex: 1;
            min-width: 200px;
            
        }

        .inventory-search1 {

            gap: 10px;
            width: 10%;
            max-width: 1000px;
           
        }

        .inventory-search1 input {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 5px;
            flex: 1;
            min-width: 200px;
            
        }

        #search-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background-color: #28a745;
            color: white;
            cursor: pointer;
            min-width: 80px;
            
        }

        #add-product-btn {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            min-width: 120px;
        }

        .inventory-filter {
            flex: 1;
            min-width: 200px;
        }

        .inventory-filter select {
            width: 100%;
            padding: 8px 10px;
            font-size: 14px;
            border-radius: 5px;
            border: 1px solid #ccc;
        }

        .inventory-list table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #aaa;
            font-weight: 400;
        }

        .inventory-list th,
        .inventory-list td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #888;
        }

        .inventory-list th {
            background-color: #f5f5f5;
        }

        .stock-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stock-input {
            width: 50px;
            padding: 6px;
            border: none;
            border-radius: 4px;
            background-color: transparent;
        }

        .low-stock {
            border-radius: 5px;
            width: 40px;
            background-color: #f04c59;
            color: #721c24;
            font-weight: bold;
        }

        .sufficient-stock {
            border-radius: 5px;
            width: 40px;
            background-color: #89e49e;
            color: #155724;
            font-weight: bold;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .action-buttons .btn {
            width: 100%;
            max-width: 80px;
            padding: 6px 12px;
            font-size: 0.9em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            overflow: auto;
        }

        .modal.show-slide {
            display: block;
            animation: slideDown 0.3s ease forwards;
        }

        @keyframes slideDown {
            0% { opacity: 0; transform: translateY(-50px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 90%;
            max-width: 600px;
            border-radius: 5px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close-modal {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-modal:hover {
            color: black;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 1em;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1em;
        }

        .form-actions {
            text-align: right;
            margin-top: 20px;
        }

        .product-image {
            max-width: 50px;
            max-height: 50px;
            object-fit: cover;
            border-radius: 4px;
        }

        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .image-preview-item {
            text-align: center;
            border: 1px solid #ddd;
            padding: 8px;
            border-radius: 6px;
            position: relative;
            background: #f9f9f9;
        }

        .slideshow-wrapper {
            position: relative;
            max-width: 100%;
            margin: 0 auto;
        }

        .slideshow-main img {
            width: 100%;
            max-height: 300px;
            object-fit: contain;
        }

        .slideshow-thumbnails {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .mobile-card {
            display: none;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
        }

        .mobile-card img {
            max-width: 80px;
            max-height: 80px;
            object-fit: cover;
            border-radius: 4px;
        }

        .mobile-card p {
            margin: 5px 0;
            font-size: 0.9em;
        }

        .mobile-card .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .staff-container {
                padding: 10px;
            }

            .inventory-list table {
                display: none;
            }

            .inventory-list .mobile-card {
                display: block;
            }

            .inventory-actions {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .inventory-search {
                flex-direction: column;
            }

            .inventory-search input,
            .inventory-filter select {
                min-width: 100%;
                font-size: 0.9rem;
            }

            #search-btn, #add-product-btn {
                width: 100%;
                padding: 10px;
                font-size: 0.9rem;
            }

            .modal-content {
                margin: 10px;
                padding: 15px;
                max-height: 85vh;
            }

            .image-preview-item {
                width: 100%;
                max-width: 150px;
            }

            .image-preview-item img {
                max-width: 100%;
                max-height: 100px;
            }

            .slideshow-main img {
                max-height: 200px;
            }

            .slideshow-thumbnails > div {
                width: 50px;
                height: 50px;
            }

            .pagination .page-link {
                padding: 6px 10px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 576px) {
            h1 {
                font-size: 1.3rem;
            }

            .form-group label {
                font-size: 0.85rem;
            }

            .form-group input,
            .form-group textarea,
            .form-group select {
                font-size: 0.85rem;
            }

            .action-buttons .btn {
                font-size: 0.85rem;
            }

            .slideshow-main img {
                max-height: 150px;
            }

            .mobile-card img {
                max-width: 60px;
            }

            .mobile-card p {
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body data-open-edit-modal="{{ open_edit_modal }}">
    {% extends "base.html" %}

    {% block title %}Products Management{% endblock %}

    {% block content %}
    <div class="staff-container staff-inventory">
        <h1 style="display: flex; justify-content: center;">Products Management</h1>

        {% if request.args.get('q') %}
        <div class="filtered-product-name" style="margin-bottom: 10px; font-weight: bold; font-size: 1.2em; color: #007bff;">
            Showing results for: "{{ request.args.get('q') }}"
        </div>
        {% endif %}

        <div class="inventory-actions">
            <div class="inventory-search">
                <label for="search-products" class="sr-only">Search</label>
                <input type="text" id="search-products" placeholder="Search products..." aria-label="Search products">
               
            </div>
             <div class="inventory-search1">
                
                
                <button class="btn btn-success" id="search-btn">Search</button>
            </div>
            
            <!-- Add Product button - Admin and Super Admin only -->
            <button class="btn btn-primary admin-only" id="add-product-btn" data-required-role="SUPER_ADMIN,ADMIN" data-action-name="add products">
                <i class="fas fa-plus"></i> Add Product
            </button>
            <div class="inventory-filter">
                <label for="brand-filter" class="sr-only">Filter by Brand</label>
                <select id="brand-filter" class="form-control" name="brand_filter" onchange="applyFilters()">
                    <option value="">All Brands</option>
                    {% for brand in brands %}
                        {% if brand.brand and brand.brand.strip() %}
                            <option value="{{ brand.brand }}">{{ brand.brand }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
            </div>
            <div class="inventory-filter">
                <label for="category-filter" class="sr-only">Filter by Category</label>
                <select id="category-filter" class="form-control" name="category_filter" onchange="applyFilters()">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}">{{ category.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="inventory-filter">
                <label for="stock-filter" class="sr-only">Filter by Stock Status</label>
                <select id="stock-filter" class="form-control" name="stock_filter" onchange="applyFilters()">
                    <option value="">All Stock Status</option>
                    <option value="low_stock">Low Stock</option>
                    <option value="in_stock">In Stock</option>
                </select>
            </div>
        </div>

        <div class="inventory-list" id="inventory-container">
            <table>
                <thead>
                    <tr>
                        <th data-sort="id" class="sortable">ID <span class="sort-indicator"></span></th>
                        <th>Image</th>
                        <th data-sort="name" class="sortable">Name <span class="sort-indicator"></span></th>
                        <th>Description</th>
                        <th data-sort="price" class="sortable">Selling Price <span class="sort-indicator"></span></th>
                        <th data-sort="original_price" class="sortable financial-only">Original Price <span class="sort-indicator"></span></th>
                        <th style="display: none;" class="financial-only">Profit Margin</th>
                        <th data-sort="stock" class="sortable">Stock <span class="sort-indicator"></span></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="inventory-table-body"></tbody>
            </table>
            <div id="mobile-inventory-list"></div>
            <nav aria-label="Page navigation example" class="mt-3">
                <ul class="pagination justify-content-center" id="pagination"></ul>
            </nav>
        </div>
    </div>

    <div id="add-product-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">×</span>
            <h2>Add New Product</h2>
            <form id="product-form" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="product-name">Product Name</label>
                    <input type="text" id="product-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="product-desc">Description</label>
                    <textarea id="product-desc" name="description" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="product-price">Selling Price</label>
                    <input type="number" id="product-price" name="price" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label for="product-original-price">Original Price (Cost from Supplier)</label>
                    <input type="number" id="product-original-price" name="original_price" step="0.01" min="0">
                </div>
                <div class="form-group">
                    <label for="product-stock">Initial Stock</label>
                    <input type="number" id="product-stock" name="stock" min="0" required>
                </div>
                <div class="form-group">
                    <label for="product-category">Category *</label>
                    <select id="product-category" name="category" required>
                        <option value="">Select Category</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="product-cpu">CPU</label>
                    <input type="text" id="product-cpu" name="cpu">
                </div>
                <div class="form-group">
                    <label for="product-ram">RAM</label>
                    <input type="text" id="product-ram" name="ram">
                </div>
                <div class="form-group">
                    <label for="product-storage">Storage</label>
                    <input type="text" id="product-storage" name="storage">
                </div>
                <div class="form-group">
                    <label for="product-graphics">Graphics</label>
                    <input type="text" id="product-graphics" name="graphics">
                </div>
                <div class="form-group">
                    <label for="product-display">Display</label>
                    <input type="text" id="product-display" name="display">
                </div>
                <div class="form-group">
                    <label for="product-os">OS</label>
                    <input type="text" id="product-os" name="os">
                </div>
                <div class="form-group">
                    <label for="product-keyboard">Keyboard</label>
                    <input type="text" id="product-keyboard" name="keyboard">
                </div>
                <div class="form-group">
                    <label for="product-battery">Battery</label>
                    <input type="text" id="product-battery" name="battery">
                </div>
                <div class="form-group">
                    <label for="product-weight">Weight</label>
                    <input type="text" id="product-weight" name="weight">
                </div>
                <div class="form-group">
                    <label for="product-warranty-id">Warranty</label>
                    <select id="product-warranty-id" name="warranty_id">
                        <option value="">Select Warranty</option>
                        {% for warranty in warranties %}
                            <option value="{{ warranty.warranty_id }}">{{ warranty.warranty_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="product-images">Product Images (optional)</label>
                    <div style="margin-bottom: 15px;">
                        <input type="file" id="product-images" name="product_images" accept="image/*" multiple="multiple" style="margin-bottom: 8px;">
                    </div>
                    <div id="image-preview-container" class="image-preview-container" style="margin-top: 15px; display: none;">
                        <div class="preview-images"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="product-color">Color</label>
                    <select id="product-color" name="color">
                        <option value="">Select Color</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Product</button>
                </div>
            </form>
        </div>
    </div>

    <div id="edit-product-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">×</span>
            <h2>Edit Product</h2>
            <form id="edit-product-form" enctype="multipart/form-data">
                <input type="hidden" id="edit-product-id" name="id">
                <div class="form-group">
                    <label for="edit-product-name">Product Name</label>
                    <input type="text" id="edit-product-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="edit-product-desc">Description</label>
                    <textarea id="edit-product-desc" name="description" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="edit-product-price">Selling Price</label>
                    <input type="number" id="edit-product-price" name="price" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label for="edit-product-original-price">Original Price (Cost from Supplier)</label>
                    <input type="number" id="edit-product-original-price" name="original_price" step="0.01" min="0">
                </div>
                <div class="form-group">
                    <label for="edit-product-stock">Stock</label>
                    <input type="number" id="edit-product-stock" name="stock" min="0" required>
                </div>
                <div class="form-group">
                    <label for="edit-product-category">Category (optional)</label>
                    <select id="edit-product-category" name="category">
                        <option value="">Select Category</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-product-warranty-id">Warranty</label>
                    <select id="edit-product-warranty-id" name="warranty_id">
                        <option value="">Select Warranty</option>
                        {% for warranty in warranties %}
                            <option value="{{ warranty.warranty_id }}">{{ warranty.warranty_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-product-cpu">CPU</label>
                    <input type="text" id="edit-product-cpu" name="cpu">
                </div>
                <div class="form-group">
                    <label for="edit-product-ram">RAM</label>
                    <input type="text" id="edit-product-ram" name="ram">
                </div>
                <div class="form-group">
                    <label for="edit-product-storage">Storage</label>
                    <input type="text" id="edit-product-storage" name="storage">
                </div>
                <div class="form-group">
                    <label for="edit-product-graphics">Graphics</label>
                    <input type="text" id="edit-product-graphics" name="graphics">
                </div>
                <div class="form-group">
                    <label for="edit-product-display">Display</label>
                    <input type="text" id="edit-product-display" name="display">
                </div>
                <div class="form-group">
                    <label for="edit-product-os">OS</label>
                    <input type="text" id="edit-product-os" name="os">
                </div>
                <div class="form-group">
                    <label for="edit-product-keyboard">Keyboard</label>
                    <input type="text" id="edit-product-keyboard" name="keyboard">
                </div>
                <div class="form-group">
                    <label for="edit-product-battery">Battery</label>
                    <input type="text" id="edit-product-battery" name="battery">
                </div>
                <div class="form-group">
                    <label for="edit-product-weight">Weight</label>
                    <input type="text" id="edit-product-weight" name="weight">
                </div>
                <div class="form-group">
                    <label for="edit-product-images">Product Images (optional)</label>
                    <div style="margin-bottom: 15px;">
                        <input type="file" id="edit-product-images" name="product_images" accept="image/*" multiple="multiple" style="margin-bottom: 8px;">
                    </div>
                    <div id="edit-image-preview-container" class="image-preview-container" style="margin-top: 15px; display: none;">
                        <div class="preview-images"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="edit-product-color">Color</label>
                    <select id="edit-product-color" name="color">
                        <option value="">Select Color</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Product</button>
                </div>
            </form>
        </div>
    </div>

    <div id="product-detail-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">×</span>
            <h2 id="product-detail-name">Product Details</h2>
            <div id="product-detail-content">
                <div id="product-slideshow-container" style="margin-bottom: 20px;">
                    <div class="slideshow-wrapper">
                        <div class="slideshow-main" style="position: relative; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; overflow: hidden;">
                            <img id="slideshow-main-image" src="" alt="Product Image" style="width: 100%;">
                            <button class="slideshow-prev" onclick="changeSlide(-1)" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); background: none; color: #666; border: none; font-size: 24px; cursor: pointer; display: none; line-height: 1; padding: 10px; width: 40px; height: 40px;" title="Previous image">‹</button>
                            <button class="slideshow-next" onclick="changeSlide(1)" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; color: #666; border: none; font-size: 24px; cursor: pointer; display: none; line-height: 1; padding: 10px; width: 40px; height: 40px;" title="Next image">›</button>
                            <div class="slideshow-counter" style="position: absolute; bottom: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; display: none;">
                                <span id="current-slide">1</span> / <span id="total-slides">1</span>
                            </div>
                        </div>
                        <div class="slideshow-thumbnails"></div>
                    </div>
                </div>
                <div id="product-details-section"></div>
            </div>
        </div>
    </div>

    <div id="bulk-update-stock-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">×</span>
            <h2>Bulk Update Stock</h2>
            <form id="bulk-stock-form">
                <div class="form-group">
                    <label for="bulk-stock-value">New Stock Value</label>
                    <input type="number" id="bulk-stock-value" min="0" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Update Stock</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Message Container for Notifications -->
    <div id="message-container"></div>
    {% endblock %}

    {% block scripts %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="/static/js/item-counter.js"></script>
    <script src="/static/js/staff_messages.js"></script>
    <script src="/static/js/product_colors.js"></script>
    <script src="/static/js/staff_inventory.js"></script>
    {% endblock %}
</body>
</html>