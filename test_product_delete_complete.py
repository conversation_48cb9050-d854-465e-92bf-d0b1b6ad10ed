#!/usr/bin/env python3
"""
Complete test script to verify the Product.delete() method works correctly
including creating a test product and deleting it successfully.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import app
from models import Product, get_db
import mysql.connector

def test_complete_product_delete():
    """Test the Product.delete() method with a complete workflow"""
    print("🧪 Testing complete Product.delete() workflow...")
    
    try:
        with app.app_context():
            # Step 1: Create a test product
            print("\n📝 Step 1: Creating a test product...")
            test_product_id = Product.create(
                name="TEST DELETE PRODUCT - DO NOT BUY",
                description="This is a test product for deletion testing",
                price=99.99,
                stock=10,
                category_id=1  # Assuming category 1 exists
            )
            print(f"✅ Created test product with ID: {test_product_id}")
            
            # Step 2: Add some inventory records for this product
            print("\n📦 Step 2: Adding inventory records...")
            conn = get_db()
            cur = conn.cursor()
            
            # Add a few inventory change records
            cur.execute("""
                INSERT INTO inventory (product_id, changes, change_date)
                VALUES (%s, %s, NOW())
            """, (test_product_id, 5))
            
            cur.execute("""
                INSERT INTO inventory (product_id, changes, change_date)
                VALUES (%s, %s, NOW())
            """, (test_product_id, -2))
            
            conn.commit()
            
            # Verify inventory records were created
            cur.execute("SELECT COUNT(*) FROM inventory WHERE product_id = %s", (test_product_id,))
            inventory_count = cur.fetchone()[0]
            print(f"✅ Added {inventory_count} inventory records")
            
            cur.close()
            conn.close()
            
            # Step 3: Verify the product exists
            print("\n🔍 Step 3: Verifying product exists...")
            product = Product.get_by_id(test_product_id)
            if product:
                print(f"✅ Product found: {product['name']}")
            else:
                print("❌ ERROR: Test product not found!")
                return False
            
            # Step 4: Attempt to delete the product (should succeed)
            print(f"\n🗑️  Step 4: Attempting to delete test product (ID: {test_product_id})...")
            try:
                success = Product.delete(test_product_id)
                if success:
                    print("✅ SUCCESS: Product deleted successfully!")
                else:
                    print("❌ ERROR: Deletion returned False")
                    return False
            except Exception as e:
                print(f"❌ ERROR: Deletion failed with error: {e}")
                return False
            
            # Step 5: Verify the product and inventory records are gone
            print("\n🔍 Step 5: Verifying deletion was complete...")
            
            # Check if product is gone
            product = Product.get_by_id(test_product_id)
            if product:
                print("❌ ERROR: Product still exists after deletion!")
                return False
            else:
                print("✅ Product successfully removed from database")
            
            # Check if inventory records are gone
            conn = get_db()
            cur = conn.cursor()
            cur.execute("SELECT COUNT(*) FROM inventory WHERE product_id = %s", (test_product_id,))
            remaining_inventory = cur.fetchone()[0]
            cur.close()
            conn.close()
            
            if remaining_inventory > 0:
                print(f"❌ ERROR: {remaining_inventory} inventory records still exist!")
                return False
            else:
                print("✅ All inventory records successfully removed")
            
            print("\n🎉 Complete deletion test passed!")
            return True
            
    except Exception as e:
        print(f"❌ ERROR: Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        
        # Cleanup: try to delete the test product if it was created
        try:
            with app.app_context():
                if 'test_product_id' in locals():
                    print(f"\n🧹 Attempting cleanup of test product {test_product_id}...")
                    Product.delete(test_product_id)
                    print("✅ Cleanup successful")
        except:
            print("⚠️  Cleanup failed - you may need to manually delete the test product")
        
        return False

if __name__ == "__main__":
    print("🚀 Starting Complete Product Delete Test...")
    success = test_complete_product_delete()
    
    if success:
        print("\n✅ All tests passed! The Product.delete() method is working perfectly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
