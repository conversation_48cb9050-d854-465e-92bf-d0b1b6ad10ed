#!/usr/bin/env python3
"""
Test script to verify the Product.delete() method works correctly
and handles foreign key constraints properly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import app
from models import Product, get_db
import mysql.connector

def test_product_delete():
    """Test the Product.delete() method"""
    print("🧪 Testing Product.delete() method...")
    
    try:
        # First, let's check if there are any products in the database
        products = Product.get_all()
        print(f"📊 Found {len(products)} products in database")
        
        if not products:
            print("❌ No products found in database. Cannot test deletion.")
            return False
            
        # Find a product that might have inventory records
        conn = get_db()
        cur = conn.cursor()
        
        # Check which products have inventory records
        cur.execute("""
            SELECT p.id, p.name, COUNT(i.id) as inventory_count
            FROM products p
            LEFT JOIN inventory i ON p.id = i.product_id
            GROUP BY p.id, p.name
            ORDER BY inventory_count DESC
            LIMIT 5
        """)
        
        products_with_inventory = cur.fetchall()
        print("\n📋 Products with inventory records:")
        for product_id, name, count in products_with_inventory:
            print(f"  - Product ID {product_id}: {name} ({count} inventory records)")
        
        # Check which products have order items
        cur.execute("""
            SELECT p.id, p.name, COUNT(oi.id) as order_items_count
            FROM products p
            LEFT JOIN order_items oi ON p.id = oi.product_id
            GROUP BY p.id, p.name
            HAVING order_items_count > 0
            ORDER BY order_items_count DESC
            LIMIT 5
        """)
        
        products_with_orders = cur.fetchall()
        print("\n📋 Products with order items:")
        for product_id, name, count in products_with_orders:
            print(f"  - Product ID {product_id}: {name} ({count} order items)")
        
        cur.close()
        conn.close()
        
        # Test 1: Try to delete a product with order items (should fail)
        if products_with_orders:
            test_product_id = products_with_orders[0][0]
            test_product_name = products_with_orders[0][1]
            print(f"\n🧪 Test 1: Attempting to delete product with order history (ID: {test_product_id}, Name: {test_product_name})")
            
            try:
                Product.delete(test_product_id)
                print("❌ ERROR: Deletion should have failed but succeeded!")
                return False
            except ValueError as e:
                print(f"✅ SUCCESS: Deletion correctly failed with error: {e}")
            except Exception as e:
                print(f"❌ ERROR: Unexpected error: {e}")
                return False
        
        # Test 2: Try to delete a product that only has inventory records (should succeed)
        # Find a product with inventory but no order items
        conn = get_db()
        cur = conn.cursor()
        
        cur.execute("""
            SELECT p.id, p.name
            FROM products p
            LEFT JOIN inventory i ON p.id = i.product_id
            LEFT JOIN order_items oi ON p.id = oi.product_id
            WHERE i.id IS NOT NULL AND oi.id IS NULL
            LIMIT 1
        """)
        
        result = cur.fetchone()
        cur.close()
        conn.close()
        
        if result:
            test_product_id, test_product_name = result
            print(f"\n🧪 Test 2: Attempting to delete product with only inventory records (ID: {test_product_id}, Name: {test_product_name})")
            
            try:
                success = Product.delete(test_product_id)
                if success:
                    print(f"✅ SUCCESS: Product {test_product_id} deleted successfully!")
                else:
                    print("❌ ERROR: Deletion returned False")
                    return False
            except Exception as e:
                print(f"❌ ERROR: Deletion failed with error: {e}")
                return False
        else:
            print("\n⚠️  No suitable product found for Test 2 (product with inventory but no orders)")
        
        # Test 3: Try to delete a non-existent product (should fail)
        print(f"\n🧪 Test 3: Attempting to delete non-existent product (ID: 99999)")
        try:
            Product.delete(99999)
            print("❌ ERROR: Deletion should have failed but succeeded!")
            return False
        except ValueError as e:
            print(f"✅ SUCCESS: Deletion correctly failed with error: {e}")
        except Exception as e:
            print(f"❌ ERROR: Unexpected error: {e}")
            return False
        
        print("\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Product Delete Test...")

    # Run the test within Flask application context
    with app.app_context():
        success = test_product_delete()

    if success:
        print("\n✅ All tests passed! The Product.delete() method is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
